# VueRouteParser 测试

这个目录包含了 VueRouteParser 类的完整测试套件。

## 测试文件

- `VueRouteParser.test.js` - VueRouteParser 类的主要测试文件

## 运行测试

### 运行所有测试
```bash
npm test -- test/domain/runtime-validation/router/VueRouteParser.test.js
```

### 运行测试并生成覆盖率报告
```bash
npm test -- test/domain/runtime-validation/router/VueRouteParser.test.js --coverage --collectCoverageFrom="src/domain/runtime-validation/router/VueRouteParser.js"
```

### 运行特定测试组
```bash
# 只运行构造函数测试
npm test -- test/domain/runtime-validation/router/VueRouteParser.test.js -t "Constructor"

# 只运行静态解析测试
npm test -- test/domain/runtime-validation/router/VueRouteParser.test.js -t "staticParseRoutes"

# 只运行 AI 解析测试
npm test -- test/domain/runtime-validation/router/VueRouteParser.test.js -t "AI Parsing"
```

## 测试覆盖的功能

### 核心功能
- ✅ 构造函数和选项配置
- ✅ 路由文件查找 (`findRouterFiles`)
- ✅ 静态路由解析 (`staticParseRoutes`)
- ✅ 完整的路由解析流程 (`parseRoutes`)

### 路由格式支持
- ✅ Vue 2 VueRouter 格式
- ✅ Vue 3 createRouter 格式
- ✅ 嵌套路由解析
- ✅ 路由元信息 (meta) 解析
- ✅ 动态导入组件解析

### AI 功能
- ✅ AI 解析回退机制
- ✅ AI 响应标准化
- ✅ 错误处理

### 路径处理
- ✅ 路由路径提取 (`getAllRoutePaths`)
- ✅ 组件路径解析
- ✅ 路由到组件的映射
- ✅ 路径推断功能

### 工具方法
- ✅ 路径标准化
- ✅ 组件信息提取
- ✅ 错误处理

## 测试覆盖率

当前测试覆盖率：**61.84%**

- 语句覆盖率：61.84%
- 分支覆盖率：47.93%
- 函数覆盖率：75.55%
- 行覆盖率：61.8%

## Mock 和测试工具

### AI Service Mock
测试中使用了 AI Service 的 mock 实现，避免了实际的 AI API 调用：

```javascript
jest.mock('../../../../src/ai/AiService', () => ({
  AIService: class MockAIService {
    // Mock 实现
  }
}));
```

### 测试工具
- 使用全局 `testUtils` 创建临时测试目录
- 自动清理测试文件和目录
- 模拟文件系统操作

## 测试数据

测试使用了多种路由配置示例：

1. **简单路由数组**
2. **Vue 3 createRouter 配置**
3. **Vue 2 VueRouter 配置**
4. **嵌套路由结构**
5. **带 meta 信息的路由**
6. **动态导入组件**
7. **错误的 JavaScript 语法**

## 扩展测试

如果需要添加新的测试用例，请遵循以下模式：

```javascript
describe('新功能组', () => {
  test('should 描述期望行为', async () => {
    // 准备测试数据
    // 执行被测试的方法
    // 验证结果
  });
});
```

## 注意事项

1. 测试使用临时目录，会自动清理
2. AI 功能被 mock，不会产生实际的 API 调用
3. 控制台输出被静默处理，减少测试噪音
4. 测试超时时间设置为 30 秒
